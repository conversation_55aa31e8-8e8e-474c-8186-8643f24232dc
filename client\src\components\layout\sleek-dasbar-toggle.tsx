import React, { useState, useRef, useEffect } from 'react';
import { useLocation } from 'wouter';
import { Home, ArrowLeft, ArrowR<PERSON>, Menu, X } from 'lucide-react';
import DasWosIconLogo from '@/components/daswos-icon-logo';

interface SleekDasBarToggleProps {
  className?: string;
  isExpanded?: boolean;
  onToggle?: (expanded: boolean) => void;
}

/**
 * Sleek DasBar toggle button that sits next to the search bar
 * When expanded, it overlays the search area with navigation options
 */
const SleekDasBarToggle: React.FC<SleekDasBarToggleProps> = ({
  className = '',
  isExpanded = false,
  onToggle
}) => {
  const [, navigate] = useLocation();
  const toggleRef = useRef<HTMLDivElement>(null);

  // Navigation items
  const navigationItems = [
    { id: 'home', label: 'Home', path: '/', icon: Home },
    { id: 'back', label: 'Back', action: 'back', icon: ArrowLef<PERSON> },
    { id: 'forward', label: 'Forward', action: 'forward', icon: ArrowRight },
  ];

  // Close expanded state when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (toggleRef.current && !toggleRef.current.contains(event.target as Node)) {
        onToggle?.(false);
      }
    };

    if (isExpanded) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isExpanded, onToggle]);

  const handleNavigation = (item: typeof navigationItems[0]) => {
    if (item.action === 'back') {
      if (window.history.length > 1) {
        window.history.back();
      }
    } else if (item.action === 'forward') {
      window.history.forward();
    } else if (item.path) {
      navigate(item.path);

      // Dispatch event to reset search interface if going home
      if (item.id === 'home') {
        const resetEvent = new CustomEvent('resetSearchInterface', {
          detail: { reset: true }
        });
        window.dispatchEvent(resetEvent);
      }
    }

    onToggle?.(false);
  };

  const toggleExpanded = () => {
    onToggle?.(!isExpanded);
  };

  return (
    <div ref={toggleRef} className={`relative ${className}`}>
      {/* Toggle Button */}
      <button
        onClick={toggleExpanded}
        className={`w-[38px] h-[38px] flex items-center justify-center rounded-md border transition-all duration-200 ${
          isExpanded
            ? 'bg-gray-100 dark:bg-gray-700 border-gray-400 dark:border-gray-500'
            : 'bg-white dark:bg-[#222222] border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800'
        }`}
        aria-label={isExpanded ? "Close navigation" : "Open navigation"}
        title={isExpanded ? "Close" : "DasBar"}
      >
        {isExpanded ? (
          <X className="h-4 w-4 text-gray-600 dark:text-gray-400" />
        ) : (
          <DasWosIconLogo height={16} width={16} className="text-gray-600 dark:text-gray-400" />
        )}
      </button>

      {/* Expanded Overlay - gracefully flows over and replaces the search bar */}
      {isExpanded && (
        <div className="absolute top-0 left-0 right-0 z-50 bg-white dark:bg-[#222222] border border-gray-300 dark:border-gray-600 rounded-md shadow-xl overflow-hidden" style={{ width: 'calc(100vw - 40px)', maxWidth: '932px', transform: 'translateX(-2px)' }}>
          {/* Header */}
          <div className="px-4 py-3 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <DasWosIconLogo height={20} width={20} />
                <span className="text-base font-semibold text-gray-900 dark:text-gray-100">DasBar</span>
              </div>
              <button
                onClick={() => onToggle?.(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Navigation Items */}
          <div className="py-3">
            {navigationItems.map((item) => (
              <button
                key={item.id}
                onClick={() => handleNavigation(item)}
                className="w-full px-4 py-3 text-left hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-3 text-sm text-gray-700 dark:text-gray-300 transition-colors border-b border-gray-100 dark:border-gray-700 last:border-b-0"
              >
                <item.icon className="h-5 w-5" />
                <span className="font-medium">{item.label}</span>
              </button>
            ))}
          </div>

          {/* Footer */}
          <div className="px-4 py-2 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
            <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
              Quick Navigation • DasWos
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SleekDasBarToggle;
