import React, { useState, useRef, useEffect } from 'react';
import { useLocation } from 'wouter';
import { Home, ArrowLeft, ArrowRight, Menu, X } from 'lucide-react';
import DasWosIconLogo from '@/components/daswos-icon-logo';
import { useDasbar } from '@/contexts/dasbar-context';

interface SleekDasBarToggleProps {
  className?: string;
  isExpanded?: boolean;
  onToggle?: (expanded: boolean) => void;
  // Pass through the search interface props for the feature buttons
  aiModeEnabled?: boolean;
  onToggleAi?: (enabled: boolean) => void;
  activeSphere?: 'safesphere' | 'opensphere';
  onSphereChange?: (sphere: 'safesphere' | 'opensphere') => void;
  superSafeActive?: boolean;
  onToggleSuperSafe?: (active: boolean) => void;
}

/**
 * Sleek DasBar toggle button that sits next to the search bar
 * When expanded, it overlays the search area with navigation options
 */
const SleekDasBarToggle: React.FC<SleekDasBarToggleProps> = ({
  className = '',
  isExpanded = false,
  onToggle,
  aiModeEnabled = false,
  onToggleAi,
  activeSphere = 'safesphere',
  onSphereChange,
  superSafeActive = false,
  onToggleSuperSafe
}) => {
  const [, navigate] = useLocation();
  const toggleRef = useRef<HTMLDivElement>(null);
  const { items } = useDasbar();

  // Basic navigation items (Home, Back, Forward)
  const basicNavItems = [
    { id: 'home', label: 'Home', path: '/', icon: Home },
    { id: 'back', label: 'Back', action: 'back', icon: ArrowLeft },
    { id: 'forward', label: 'Forward', action: 'forward', icon: ArrowRight },
  ];

  // Close expanded state when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (toggleRef.current && !toggleRef.current.contains(event.target as Node)) {
        onToggle?.(false);
      }
    };

    if (isExpanded) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isExpanded, onToggle]);

  const handleNavigation = (item: any) => {
    if (item.action === 'back') {
      if (window.history.length > 1) {
        window.history.back();
      }
    } else if (item.action === 'forward') {
      window.history.forward();
    } else if (item.path) {
      navigate(item.path);

      // Dispatch event to reset search interface if going home
      if (item.id === 'home') {
        const resetEvent = new CustomEvent('resetSearchInterface', {
          detail: { reset: true }
        });
        window.dispatchEvent(resetEvent);
      }
    }

    onToggle?.(false);
  };

  const toggleExpanded = () => {
    onToggle?.(!isExpanded);
  };

  return (
    <div ref={toggleRef} className={`flex items-center gap-2 ${className}`}>
      {/* Toggle Button */}
      <button
        onClick={toggleExpanded}
        className={`w-[38px] h-[38px] flex items-center justify-center rounded-md border transition-all duration-200 ${
          isExpanded
            ? 'bg-gray-100 dark:bg-gray-700 border-gray-400 dark:border-gray-500'
            : 'bg-white dark:bg-[#222222] border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800'
        }`}
        aria-label={isExpanded ? "Close navigation" : "Open navigation"}
        title={isExpanded ? "Close" : "DasBar"}
      >
        {isExpanded ? (
          <X className="h-4 w-4 text-gray-600 dark:text-gray-400" />
        ) : (
          <DasWosIconLogo height={16} width={16} className="text-gray-600 dark:text-gray-400" />
        )}
      </button>

      {/* Expanded Navigation Buttons - shown inline when expanded */}
      {isExpanded && (
        <>
          {/* Basic navigation items */}
          {basicNavItems.map((item) => (
            <button
              key={item.id}
              onClick={() => handleNavigation(item)}
              className="w-[38px] h-[38px] bg-black dark:bg-white text-white dark:text-black rounded-md flex items-center justify-center hover:bg-gray-800 dark:hover:bg-gray-200 transition-colors"
              title={item.label}
            >
              <item.icon className="h-4 w-4" />
            </button>
          ))}

          {/* DasBar items from context */}
          {items.map((item) => (
            <button
              key={item.id}
              onClick={() => handleNavigation(item)}
              className="w-[38px] h-[38px] bg-black dark:bg-white text-white dark:text-black rounded-md flex items-center justify-center hover:bg-gray-800 dark:hover:bg-gray-200 transition-colors"
              title={item.label}
            >
              {item.icon && React.createElement(item.icon, { className: "h-4 w-4" })}
            </button>
          ))}

          {/* Close button */}
          <button
            onClick={() => onToggle?.(false)}
            className="w-[38px] h-[38px] bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-md flex items-center justify-center hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            title="Close DasBar"
          >
            <X className="h-4 w-4" />
          </button>
        </>
      )}
    </div>
  );
};

export default SleekDasBarToggle;
