import React, { useState, useRef, useEffect } from 'react';
import { useLocation } from 'wouter';
import { Home, ArrowLeft, ArrowRight, Menu, X } from 'lucide-react';
import DasWosIconLogo from '@/components/daswos-icon-logo';
import { useDasbar } from '@/contexts/dasbar-context';

interface SleekDasBarToggleProps {
  className?: string;
  isExpanded?: boolean;
  onToggle?: (expanded: boolean) => void;
  // Pass through the search interface props for the feature buttons
  aiModeEnabled?: boolean;
  onToggleAi?: (enabled: boolean) => void;
  activeSphere?: 'safesphere' | 'opensphere';
  onSphereChange?: (sphere: 'safesphere' | 'opensphere') => void;
  superSafeActive?: boolean;
  onToggleSuperSafe?: (active: boolean) => void;
}

/**
 * Sleek DasBar toggle button that sits next to the search bar
 * When expanded, it overlays the search area with navigation options
 */
const SleekDasBarToggle: React.FC<SleekDasBarToggleProps> = ({
  className = '',
  isExpanded = false,
  onToggle,
  aiModeEnabled = false,
  onToggleAi,
  activeSphere = 'safesphere',
  onSphereChange,
  superSafeActive = false,
  onToggleSuperSafe
}) => {
  const [, navigate] = useLocation();
  const toggleRef = useRef<HTMLDivElement>(null);
  const { items } = useDasbar();

  // Basic navigation items (Home, Back, Forward)
  const basicNavItems = [
    { id: 'home', label: 'Home', path: '/', icon: Home },
    { id: 'back', label: 'Back', action: 'back', icon: ArrowLeft },
    { id: 'forward', label: 'Forward', action: 'forward', icon: ArrowRight },
  ];

  // Close expanded state when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (toggleRef.current && !toggleRef.current.contains(event.target as Node)) {
        onToggle?.(false);
      }
    };

    if (isExpanded) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isExpanded, onToggle]);

  const handleNavigation = (item: any) => {
    if (item.action === 'back') {
      if (window.history.length > 1) {
        window.history.back();
      }
    } else if (item.action === 'forward') {
      window.history.forward();
    } else if (item.path) {
      navigate(item.path);

      // Dispatch event to reset search interface if going home
      if (item.id === 'home') {
        const resetEvent = new CustomEvent('resetSearchInterface', {
          detail: { reset: true }
        });
        window.dispatchEvent(resetEvent);
      }
    }

    onToggle?.(false);
  };

  const toggleExpanded = () => {
    onToggle?.(!isExpanded);
  };

  return (
    <div ref={toggleRef} className={`relative ${className}`}>
      {/* Toggle Button */}
      <button
        onClick={toggleExpanded}
        className={`w-[38px] h-[38px] flex items-center justify-center rounded-md border transition-all duration-200 ${
          isExpanded
            ? 'bg-gray-100 dark:bg-gray-700 border-gray-400 dark:border-gray-500'
            : 'bg-white dark:bg-[#222222] border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800'
        }`}
        aria-label={isExpanded ? "Close navigation" : "Open navigation"}
        title={isExpanded ? "Close" : "DasBar"}
      >
        {isExpanded ? (
          <X className="h-4 w-4 text-gray-600 dark:text-gray-400" />
        ) : (
          <DasWosIconLogo height={16} width={16} className="text-gray-600 dark:text-gray-400" />
        )}
      </button>

      {/* Expanded Overlay - gracefully flows over and replaces the search bar */}
      {isExpanded && (
        <div className="absolute top-0 left-0 right-0 z-50 bg-white dark:bg-[#222222] border border-gray-300 dark:border-gray-600 rounded-md shadow-xl overflow-hidden" style={{ width: 'calc(100vw - 40px)', maxWidth: '932px', transform: 'translateX(-2px)' }}>
          {/* DasBar Navigation Buttons */}
          <div className="px-4 py-3">
            <div className="flex items-center justify-center space-x-2 flex-wrap">
              {/* Basic navigation items first */}
              {basicNavItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleNavigation(item)}
                  className="w-[40px] h-[40px] bg-black dark:bg-white text-white dark:text-black rounded-md flex items-center justify-center hover:bg-gray-800 dark:hover:bg-gray-200 transition-colors"
                  title={item.label}
                >
                  <item.icon className="h-5 w-5" />
                </button>
              ))}

              {/* DasBar items from context */}
              {items.map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleNavigation(item)}
                  className="w-[40px] h-[40px] bg-black dark:bg-white text-white dark:text-black rounded-md flex items-center justify-center hover:bg-gray-800 dark:hover:bg-gray-200 transition-colors"
                  title={item.label}
                >
                  {item.icon && React.createElement(item.icon, { className: "h-5 w-5" })}
                </button>
              ))}

              {/* Close button */}
              <button
                onClick={() => onToggle?.(false)}
                className="w-[40px] h-[40px] bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-md flex items-center justify-center hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors ml-2"
                title="Close DasBar"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Feature buttons row */}
            <div className="flex justify-center space-x-2 px-4 pb-3">
              {/* SafeSphere button */}
              <div
                className={`bg-white dark:bg-gray-800 rounded-sm shadow-sm border border-gray-300 dark:border-gray-600 inline-flex items-center px-2 py-1 ${activeSphere === 'safesphere' ? 'w-[160px]' : 'w-[120px]'} cursor-pointer transition-all duration-200`}
                onClick={() => onSphereChange && onSphereChange(activeSphere === 'safesphere' ? 'opensphere' : 'safesphere')}
              >
                {/* Square checkbox */}
                <div className="w-4 h-4 border border-gray-400 dark:border-gray-500 bg-white dark:bg-gray-700 flex items-center justify-center mr-2 flex-shrink-0">
                  {activeSphere === 'safesphere' && (
                    <svg className="w-4 h-4 text-gray-800 dark:text-gray-200" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M5 12l5 5L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                    </svg>
                  )}
                </div>

                {/* Shield icon */}
                <svg className="h-3.5 w-3.5 mr-1.5 text-gray-700 dark:text-gray-300 flex-shrink-0" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                </svg>

                {/* Text */}
                <span className="text-gray-900 dark:text-gray-100 font-medium text-xs flex-shrink-0 whitespace-nowrap w-[70px]">SafeSphere</span>

                {/* Status label - only shown when active */}
                {activeSphere === 'safesphere' && (
                  <span className="ml-auto text-green-500 text-[8px] font-medium w-[55px] text-right pr-1">Protected</span>
                )}
              </div>

              {/* DasWos AI button */}
              <div
                className={`bg-white dark:bg-gray-800 rounded-sm shadow-sm border border-gray-300 dark:border-gray-600 inline-flex items-center px-2 py-1 ${aiModeEnabled ? 'w-[160px]' : 'w-[120px]'} cursor-pointer transition-all duration-200`}
                onClick={() => onToggleAi && onToggleAi(!aiModeEnabled)}
              >
                {/* Square checkbox */}
                <div className="w-4 h-4 border border-gray-400 dark:border-gray-500 bg-white dark:bg-gray-700 flex items-center justify-center mr-2 flex-shrink-0">
                  {aiModeEnabled && (
                    <svg className="w-4 h-4 text-gray-800 dark:text-gray-200" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M5 12l5 5L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                    </svg>
                  )}
                </div>

                {/* TV/Computer icon */}
                <svg className="h-3.5 w-3.5 mr-1.5 text-gray-700 dark:text-gray-300 flex-shrink-0" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></rect>
                  <line x1="8" y1="21" x2="16" y2="21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></line>
                  <line x1="12" y1="17" x2="12" y2="21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></line>
                </svg>

                {/* Text */}
                <span className="text-gray-900 dark:text-gray-100 font-medium text-xs flex-shrink-0 whitespace-nowrap w-[70px]">Daswos AI</span>

                {/* Status label - only shown when active */}
                {aiModeEnabled && (
                  <span className="ml-auto text-blue-500 text-[8px] font-medium w-[50px] text-right pr-1">Enabled</span>
                )}
              </div>

              {/* SuperSafe button */}
              <div
                className={`bg-white dark:bg-gray-800 rounded-sm shadow-sm border border-gray-300 dark:border-gray-600 inline-flex items-center px-2 py-1 ${superSafeActive ? 'w-[160px]' : 'w-[120px]'} cursor-pointer transition-all duration-200`}
                onClick={() => onToggleSuperSafe && onToggleSuperSafe(!superSafeActive)}
              >
                {/* Square checkbox */}
                <div className="w-4 h-4 border border-gray-400 dark:border-gray-500 bg-white dark:bg-gray-700 flex items-center justify-center mr-2 flex-shrink-0">
                  {superSafeActive && (
                    <svg className="w-4 h-4 text-gray-800 dark:text-gray-200" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M5 12l5 5L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                    </svg>
                  )}
                </div>

                {/* Circle check icon */}
                <svg className="h-3.5 w-3.5 mr-1.5 text-gray-700 dark:text-gray-300 flex-shrink-0" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                </svg>

                {/* Text */}
                <span className="text-gray-900 dark:text-gray-100 font-medium text-xs flex-shrink-0 whitespace-nowrap w-[70px]">SuperSafe</span>

                {/* Status label - only shown when active */}
                {superSafeActive && (
                  <span className="ml-auto text-green-500 text-[8px] font-medium w-[35px] text-right pr-1">Active</span>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SleekDasBarToggle;
